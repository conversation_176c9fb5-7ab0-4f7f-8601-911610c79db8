name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build plugin
      run: npm run build
      
    - name: Install Obsidian (Linux)
      run: |
        # Download and install Obsidian for Linux
        wget https://github.com/obsidianmd/obsidian-releases/releases/download/v1.4.16/Obsidian-1.4.16.AppImage
        chmod +x Obsidian-1.4.16.AppImage
        sudo mv Obsidian-1.4.16.AppImage /usr/local/bin/obsidian
        
    - name: Run E2E tests in headless mode
      run: |
        # Use xvfb for virtual display and run tests in CI mode
        xvfb-run -a npm run test:e2e:ci
      env:
        CI: true
        
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-test-results
        path: |
          test-results/
          playwright-report/
        retention-days: 7
