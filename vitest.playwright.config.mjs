import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'node', // Use node environment for Playwright tests
    globals: true,
    // Use a setup file that manages a single Electron instance across all tests
    setupFiles: ['./e2e/setup-shared-electron.ts'],
    include: [
      'e2e/**/*.e2e.ts', // All e2e tests now use Playwright/Electron setup
    ],
    exclude: [
      'tests/**/*',                 // Exclude unit tests
    ],
    testTimeout: 120000, // 2 minutes for e2e tests
    hookTimeout: 60000,  // 1 minute for setup/teardown hooks (<PERSON><PERSON> needs more time)
    // Playwright tests should run sequentially to avoid conflicts
    fileParallelism: false,
    maxConcurrency: 1,
    // Run tests in threads to share the same process and global state
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true
      }
    }
  },
  define: {
    global: 'globalThis'
  }
});
