#!/usr/bin/env bash

set -eu -o pipefail

root_path=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")/.." &>/dev/null && pwd)

obsidian_app_path="${1:-/Applications/Obsidian.app}"
vault_path="$root_path/tests/vault/Test"
obsidian_unpacked_path="$root_path/.obsidian-unpacked"
plugin_path="$vault_path/.obsidian/plugins/ghost-sync"

echo "🔧 Setting up Obsidian for Playwright testing..."
echo

if [ ! -e "$obsidian_app_path" ]; then
    echo "❌ Obsidian not found at $obsidian_app_path. Make sure Obsidian is installed."
    exit 1
fi

if [ ! -d "$vault_path" ]; then
    echo "❌ Test vault not found at $vault_path. Make sure this script is running from the root of the repository."
    exit 2
fi

echo "📦 Unpacking Obsidian from $obsidian_app_path to $obsidian_unpacked_path..."
rm -rf "$obsidian_unpacked_path"
npx @electron/asar extract "$obsidian_app_path/Contents/Resources/app.asar" "$obsidian_unpacked_path"
cp -f "$obsidian_app_path/Contents/Resources/obsidian.asar" "$obsidian_unpacked_path/obsidian.asar"
echo "✅ Obsidian unpacked successfully."

echo
echo "🔨 Building plugin..."
npm run build
echo "✅ Plugin built successfully."

echo
echo "ℹ️ Plugin files will be automatically built and updated during test runs."

echo
echo "⚙️  Obsidian will now start with existing e2e test data. Please:"
echo "  1. Verify the Test vault opens automatically"
echo "  2. Enable community plugins in Settings > Community plugins (if not already enabled)"
echo "  3. Enable the 'Ghost Sync' plugin (if not already enabled)"
echo "  4. Close Obsidian when done"
echo
read -rp "Press [ENTER] to continue..."
npx electron "$obsidian_unpacked_path/main.js" --user-data-dir="$root_path/e2e/test_obsidian_data" &>/dev/null
echo "✅ Setup complete!"

echo
echo "🎯 You can now run Playwright Electron tests with:"
echo "   npm run test:e2e:playwright"
echo
echo "📁 Files created:"
echo "   - $obsidian_unpacked_path (unpacked Obsidian)"
echo "   - $plugin_path (plugin symlinks)"
