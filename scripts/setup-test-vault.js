#!/usr/bin/env node

/**
 * Unified setup script for test vault
 * 
 * This script provides a single setup step that:
 * 1. Builds the plugin
 * 2. Copies plugin files to the test pristine vault
 * 3. Ensures the test vault is ready for e2e tests
 * 
 * Usage:
 *   node scripts/setup-test-vault.js
 *   npm run setup:test-vault
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const projectRoot = path.join(__dirname, '..');
const pristineVaultPath = path.join(projectRoot, 'tests/vault/Test.pristine');
const testVaultPath = path.join(projectRoot, 'tests/vault/Test');
const pristinePluginDir = path.join(pristineVaultPath, '.obsidian/plugins/ghost-sync');
const testPluginDir = path.join(testVaultPath, '.obsidian/plugins/ghost-sync');

console.log('🔧 Setting up test vault with latest plugin build...');

/**
 * Build the plugin
 */
function buildPlugin() {
  console.log('📦 Building plugin...');
  try {
    execSync('npm run build', { 
      cwd: projectRoot, 
      stdio: 'inherit' 
    });
    console.log('✅ Plugin built successfully');
  } catch (error) {
    console.error('❌ Failed to build plugin:', error.message);
    process.exit(1);
  }
}

/**
 * Copy plugin files to a directory
 */
function copyPluginFiles(targetDir, description) {
  console.log(`📋 Copying plugin files to ${description}...`);
  
  // Ensure plugin directory exists
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
    console.log(`📁 Created plugin directory: ${targetDir}`);
  }

  // Files to copy
  const filesToCopy = ['main.js', 'manifest.json', 'styles.css'];

  for (const file of filesToCopy) {
    const sourcePath = path.join(projectRoot, file);
    const destPath = path.join(targetDir, file);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`  ✓ Copied ${file}`);
    } else {
      console.warn(`  ⚠️  Warning: ${file} not found in project root`);
    }
  }
}

/**
 * Ensure articles directory exists in vault
 */
function ensureArticlesDirectory(vaultPath, description) {
  const articlesDir = path.join(vaultPath, 'articles');
  if (!fs.existsSync(articlesDir)) {
    fs.mkdirSync(articlesDir, { recursive: true });
    console.log(`📁 Created articles directory in ${description}`);
  }
}

/**
 * Validate that required files exist
 */
function validateBuild() {
  const requiredFiles = ['main.js', 'manifest.json', 'styles.css'];
  
  for (const file of requiredFiles) {
    const filePath = path.join(projectRoot, file);
    if (!fs.existsSync(filePath)) {
      console.error(`❌ Error: ${file} was not generated in project root`);
      console.error('💡 Check the build output above for errors');
      process.exit(1);
    }
  }
  
  console.log('✅ All required plugin files are present');
}

/**
 * Main setup function
 */
function main() {
  try {
    // Step 1: Build the plugin
    buildPlugin();
    
    // Step 2: Validate build output
    validateBuild();
    
    // Step 3: Copy plugin files to pristine vault
    copyPluginFiles(pristinePluginDir, 'pristine vault');
    
    // Step 4: Copy plugin files to test vault (for immediate use)
    copyPluginFiles(testPluginDir, 'test vault');
    
    // Step 5: Ensure articles directories exist
    ensureArticlesDirectory(pristineVaultPath, 'pristine vault');
    ensureArticlesDirectory(testVaultPath, 'test vault');
    
    console.log('');
    console.log('✅ Test vault setup complete!');
    console.log('');
    console.log('📍 Plugin locations:');
    console.log(`   Pristine vault: ${pristinePluginDir}`);
    console.log(`   Test vault: ${testPluginDir}`);
    console.log('');
    console.log('🎯 You can now run e2e tests with:');
    console.log('   npm run test:e2e');
    console.log('');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  buildPlugin,
  copyPluginFiles,
  ensureArticlesDirectory,
  validateBuild,
  main
};
