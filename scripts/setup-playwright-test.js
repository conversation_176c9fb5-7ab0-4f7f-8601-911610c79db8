#!/usr/bin/env node

/**
 * Setup script for Playwright Electron tests
 * 
 * This script:
 * 1. Builds the plugin
 * 2. Copies plugin files to the test vault
 * 3. Ensures the test vault is ready for Playwright Electron tests
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const projectRoot = path.join(__dirname, '..');
const testVaultPath = path.join(projectRoot, 'tests/vault/Test');
const pluginDir = path.join(testVaultPath, '.obsidian/plugins/ghost-sync');

console.log('🔧 Setting up Playwright Electron test environment...');

// 1. Build the plugin
console.log('📦 Building plugin...');
try {
  execSync('npm run build', { cwd: projectRoot, stdio: 'inherit' });
  console.log('✅ Plugin built successfully');
} catch (error) {
  console.error('❌ Failed to build plugin:', error.message);
  process.exit(1);
}

// 2. Ensure plugin directory exists
if (!fs.existsSync(pluginDir)) {
  fs.mkdirSync(pluginDir, { recursive: true });
  console.log('📁 Created plugin directory');
}

// 3. Copy plugin files
const filesToCopy = ['main.js', 'manifest.json', 'styles.css'];

for (const file of filesToCopy) {
  const sourcePath = path.join(projectRoot, file);
  const destPath = path.join(pluginDir, file);
  
  if (fs.existsSync(sourcePath)) {
    fs.copyFileSync(sourcePath, destPath);
    console.log(`📋 Copied ${file}`);
  } else {
    console.warn(`⚠️  Warning: ${file} not found`);
  }
}

// 4. Ensure articles directory exists
const articlesDir = path.join(testVaultPath, 'articles');
if (!fs.existsSync(articlesDir)) {
  fs.mkdirSync(articlesDir, { recursive: true });
  console.log('📁 Created articles directory');
}

// 5. Create a simple test configuration if it doesn't exist
const configPath = path.join(pluginDir, 'data.json');
if (!fs.existsSync(configPath)) {
  const defaultConfig = {
    "ghostUrl": "https://demo.ghost.io",
    "adminApiKey": "test-key",
    "articlesFolder": "articles",
    "enableSync": false
  };
  
  fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2));
  console.log('⚙️  Created default plugin configuration');
}

console.log('✅ Playwright Electron test environment ready!');
console.log('');
console.log('To run the Playwright Electron test:');
console.log('  npm run test:e2e:playwright');
console.log('');
console.log('HAR recordings will be saved to: e2e/recordings/');
