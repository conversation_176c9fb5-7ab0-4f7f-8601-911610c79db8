{"name": "obsidian-ghost-sync", "version": "1.0.0", "description": "Sync posts between Obsidian and Ghost.io", "main": "src/main.ts", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "vitest run", "test:ci": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:legacy": "node test-content-conversion.js", "test:build": "npm test && npm run build", "setup:obsidian-playwright": "./scripts/setup-obsidian-playwright.sh", "setup:test-vault": "node scripts/setup-test-vault.js", "test:e2e": "npm run setup:test-vault && npx vitest run --config vitest.playwright.config.mjs", "test:e2e:windowed": "npm run setup:test-vault && E2E_HEADLESS=false npx vitest run --config vitest.playwright.config.mjs", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@electron/asar": "^4.0.1", "@playwright/test": "^1.48.0", "@sveltejs/vite-plugin-svelte": "^5.0.4", "@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "@vitest/ui": "^2.1.8", "@wdio/cli": "^9.19.1", "@wdio/local-runner": "^9.19.1", "@wdio/mocha-framework": "^9.19.1", "@wdio/selenium-standalone-service": "^8.14.0", "@wdio/spec-reporter": "^9.19.1", "builtin-modules": "3.3.0", "chai": "^5.2.1", "chromedriver": "^139.0.1", "electron": "^37.3.1", "esbuild": "0.17.3", "esbuild-svelte": "^0.9.3", "jsdom": "^26.0.0", "obsidian": "latest", "svelte": "^5.38.1", "ts-node": "^10.9.2", "tslib": "2.4.0", "typescript": "^5.9.2", "vite": "^6.0.7", "vitest": "^2.1.8", "wdio-chromedriver-service": "^8.1.1"}, "dependencies": {"@lexical/code": "^0.34.0", "@lexical/headless": "^0.34.0", "@lexical/html": "^0.34.0", "@lexical/link": "^0.34.0", "@lexical/list": "^0.34.0", "@lexical/markdown": "^0.34.0", "@lexical/rich-text": "^0.34.0", "@lexical/table": "^0.34.0", "@lexical/text": "^0.34.0", "@lexical/utils": "^0.34.0", "@tryghost/admin-api": "^1.14.0", "@types/markdown-it": "^14.1.2", "lexical": "^0.34.0", "markdown-it": "^14.1.0", "turndown": "^7.2.0"}}