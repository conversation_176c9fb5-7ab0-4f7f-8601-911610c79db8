import { setupObsidianElectron } from './helpers/plugin-setup';
import { restoreTestVaultToPristine } from './helpers/electron-setup';
import type { ElectronApplication, Page } from 'playwright';

/**
 * Shared Electron instance management
 * This setup file ensures a single Electron instance is used across all tests
 */

let sharedElectronApp: ElectronApplication | null = null;
let sharedPage: Page | null = null;
let setupPromise: Promise<void> | null = null;

/**
 * Initialize the shared Electron instance
 * This is called once and reused across all tests
 */
async function initializeSharedElectron(): Promise<void> {
  if (sharedElectronApp && sharedPage) {
    console.log("✅ Shared Electron instance already initialized");
    return;
  }

  console.log("🌍 Initializing shared Electron instance...");

  try {
    // First, restore the test vault to pristine state
    await restoreTestVaultToPristine();

    // Launch Obsidian with Playwright/Electron
    const { electronApp, page } = await setupObsidianElectron();

    sharedElectronApp = electronApp;
    sharedPage = page;

    // Store globally for access by tests
    (global as any).__SHARED_ELECTRON_APP__ = electronApp;
    (global as any).__SHARED_ELECTRON_PAGE__ = page;

    console.log("✅ Shared Electron instance initialized successfully");

  } catch (error) {
    console.error("❌ Failed to initialize shared Electron instance:", error);
    throw error;
  }
}

/**
 * Get the shared Electron instance
 * Initializes it if not already done
 */
export async function getSharedElectron(): Promise<{ electronApp: ElectronApplication; page: Page }> {
  if (!setupPromise) {
    setupPromise = initializeSharedElectron();
  }

  await setupPromise;

  if (!sharedElectronApp || !sharedPage) {
    throw new Error("Shared Electron instance not available after initialization");
  }

  return {
    electronApp: sharedElectronApp,
    page: sharedPage
  };
}

/**
 * Cleanup the shared Electron instance
 * This should be called when all tests are done
 */
export async function cleanupSharedElectron(): Promise<void> {
  if (sharedElectronApp) {
    console.log("🔄 Cleaning up shared Electron instance...");

    try {
      if (sharedPage && !sharedPage.isClosed()) {
        await sharedPage.close();
      }
    } catch (error) {
      console.log("⚠️ Error closing page:", error.message);
    }

    try {
      await sharedElectronApp.close();
    } catch (error) {
      console.log("⚠️ Error closing Electron app:", error.message);
    }

    sharedElectronApp = null;
    sharedPage = null;
    setupPromise = null;

    // Clear global references
    delete (global as any).__SHARED_ELECTRON_APP__;
    delete (global as any).__SHARED_ELECTRON_PAGE__;

    console.log("✅ Shared Electron instance cleaned up");
  }
}

// Setup hook that runs once for all tests
if (typeof beforeAll !== 'undefined') {
  // This will run once before all tests in the entire test suite
  beforeAll(async () => {
    await getSharedElectron();
  }, 120000); // 2 minute timeout for setup
}

// Cleanup hook that runs after all tests
if (typeof afterAll !== 'undefined') {
  afterAll(async () => {
    await cleanupSharedElectron();
  }, 60000); // 1 minute timeout for cleanup
}

// Handle process exit to ensure cleanup
process.on('exit', () => {
  if (sharedElectronApp) {
    console.log("🚨 Process exiting - attempting emergency cleanup");
    // Synchronous cleanup attempt
    try {
      sharedElectronApp.close();
    } catch (error) {
      console.log("⚠️ Emergency cleanup failed:", error.message);
    }
  }
});

process.on('SIGINT', async () => {
  console.log("🚨 SIGINT received - cleaning up Electron instance");
  await cleanupSharedElectron();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log("🚨 SIGTERM received - cleaning up Electron instance");
  await cleanupSharedElectron();
  process.exit(0);
});
