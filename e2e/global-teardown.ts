import * as fs from 'fs';
import * as path from 'path';

/**
 * Global teardown for all e2e tests
 * Cleans up the shared Electron instance
 */
export default async function globalTeardown() {
  console.log("🌍 Starting global e2e test teardown...");
  
  try {
    // Get the stored Electron app and page
    const electronApp = (global as any).__ELECTRON_APP__;
    const page = (global as any).__ELECTRON_PAGE__;
    
    if (electronApp) {
      console.log("🔄 Cleaning up Electron test environment...");
      
      try {
        // Close the page first
        if (page && !page.isClosed()) {
          await page.close();
        }
      } catch (error) {
        console.log("⚠️ Error closing page:", error.message);
      }
      
      try {
        // Close the Electron app
        await electronApp.close();
      } catch (error) {
        console.log("⚠️ Error closing Electron app:", error.message);
      }
      
      console.log("✅ Electron test environment cleaned up");
    }
    
    // Clean up connection file
    const connectionFile = path.join(process.cwd(), 'e2e/.electron-connection.json');
    if (fs.existsSync(connectionFile)) {
      fs.unlinkSync(connectionFile);
      console.log("🗑️ Connection file cleaned up");
    }
    
    console.log("✅ Global e2e test teardown complete");
    
  } catch (error) {
    console.error("❌ Global teardown failed:", error);
    // Don't throw here - we want tests to complete even if cleanup fails
  }
}
