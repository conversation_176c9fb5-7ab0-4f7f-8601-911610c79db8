import { setupObsidianElectron } from './helpers/plugin-setup';
import { restoreTestVaultToPristine } from './helpers/electron-setup';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Global setup for all e2e tests
 * Launches a single Electron instance that all tests will share
 */
export default async function globalSetup() {
  console.log("🌍 Starting global e2e test setup...");

  try {
    // First, restore the test vault to pristine state
    await restoreTestVaultToPristine();

    // Launch Obsidian with Playwright/Electron
    const { electronApp, page } = await setupObsidianElectron();

    // Store connection info for tests to use
    const browser = electronApp.context().browser();
    const connectionInfo = {
      pid: electronApp.process().pid
    };

    console.log("🔗 Connection info:", connectionInfo);

    // Write connection info to a file that tests can read
    const connectionFile = path.join(process.cwd(), 'e2e/.electron-connection.json');
    fs.writeFileSync(connectionFile, JSON.stringify(connectionInfo, null, 2));

    console.log("✅ Global e2e test setup complete");
    console.log(`📝 Connection info saved to: ${connectionFile}`);

    // Store the electronApp and page globally for cleanup
    (global as any).__ELECTRON_APP__ = electronApp;
    (global as any).__ELECTRON_PAGE__ = page;

  } catch (error) {
    console.error("❌ Global setup failed:", error);
    throw error;
  }
}
